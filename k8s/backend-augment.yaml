---
apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-augment-config
  namespace: llm-eval
data:
  DATABASE_HOST: "postgresql-augment-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "llm_eval_augment"
  # OPENROUTER_API_KEY is sensitive, should remain in secret

---
apiVersion: v1
kind: Secret
metadata:
  name: backend-augment-secret
  namespace: llm-eval
type: Opaque
data:
  # Replace with your actual base64 encoded values during CI/CD
  DATABASE_USER: cG9zdGdyZXM= # postgres
  DATABASE_PASSWORD: cG9zdGdyZXMxMjM= # postgres123
  OPENROUTER_API_KEY: WU9VUl9PUEVOUk9VVEVSX0FQSV9LRVlfSEVSRQ== # Placeholder, will be replaced by CI
  DATABASE_URL: cG9zdGdyZXNxbCthc3luY3BnOi8vcG9zdGdyZXM6cG9zdGdyZXMxMjNAcG9zdGdyZXNxbC1zZXJ2aWNlOjU0MzIvbGxtX2V2YWw= # Placeholder, will be constructed and replaced by CI

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-augment
  namespace: llm-eval
  labels:
    app: backend-augment
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend-augment
  template:
    metadata:
      labels:
        app: backend-augment
    spec:
      containers:
      - name: backend-augment
        image: llm-eval-backend:latest # This will be replaced by CI with ghcr.io image
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: backend-augment-secret
              key: DATABASE_URL
        - name: OPENROUTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: backend-augment-secret
              key: OPENROUTER_API_KEY
        # Individual DB components are no longer needed here as DATABASE_URL is fully formed
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: backend-augment-service
  namespace: llm-eval
  labels:
    app: backend-augment
spec:
  selector:
    app: backend-augment
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
  type: ClusterIP 