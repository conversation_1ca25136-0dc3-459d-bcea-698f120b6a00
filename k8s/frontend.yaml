---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: llm-eval
  labels:
    app: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: llm-eval-frontend:latest
        imagePullPolicy: IfNotPresent  # For local k3s development
        ports:
        - containerPort: 3000
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: llm-eval
  labels:
    app: frontend
spec:
  selector:
    app: frontend
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
  type: LoadBalancer  # For k3s, this will expose the service externally 