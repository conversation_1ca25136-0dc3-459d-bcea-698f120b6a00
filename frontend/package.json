{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "date-fns": "^4.1.0", "katex": "^0.16.22", "lucide": "^0.510.0", "lucide-react": "^0.510.0", "prismjs": "^1.30.0", "re-resizable": "^6.11.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "rehype-katex": "^7.0.1", "rehype-prism-plus": "^2.0.1", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}