{"root": ["./src/App.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/api/apiClient.ts", "./src/components/AggregatedResultsChart.tsx", "./src/components/DeleteConfirmModal.tsx", "./src/components/EvaluateArea.tsx", "./src/components/EvaluatorConsistencyTable.tsx", "./src/components/HistorySidebar.tsx", "./src/components/LoadingSpinner.tsx", "./src/components/ModelSelector.tsx", "./src/components/OutputDisplay.tsx", "./src/components/PromptDisplay.tsx", "./src/components/PromptInput.tsx", "./src/components/ReportViewer.tsx", "./src/components/SingleOutputDisplay.tsx", "./src/components/SystemStatus.tsx", "./src/components/TaskUsageStatsDisplay.tsx", "./src/components/ThemeToggle.tsx", "./src/components/UsageStatsDisplay.tsx", "./src/context/ThemeContext.tsx", "./src/pages/EvaluationPlatformPage.tsx", "./src/pages/NewConversationPage.tsx", "./src/pages/ViewTaskPage.tsx", "./src/store/healthStore.ts", "./src/store/taskStore.ts", "./src/types/custom.d.ts"], "errors": true, "version": "5.8.3"}