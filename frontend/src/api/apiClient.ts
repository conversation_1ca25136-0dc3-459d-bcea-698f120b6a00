import axios from 'axios';

// Create an axios instance with default config
const apiClient = axios.create({
  baseURL: '/api/v1', // Matches our backend API prefix
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout
});

// Types for API responses and requests - matches backend schemas
export interface TaskCreateRequest {
  prompt: string;
  system_prompt?: string;  // Add system_prompt field
  models_to_generate?: string[];
}

export interface TaskCreateResponse {
  task_id: number;
  status: string;
  requested_models: string[];
  message: string;
}

export interface Generation {
  id: number;
  task_id: number;
  model_id_used: string;
  blind_id: string;
  output_text: string | null;
  reasoning_text?: string | null;
  error_message: string | null;
  created_at: string; // ISO date string

  // Usage statistics fields
  prompt_tokens?: number | null;
  completion_tokens?: number | null;
  total_tokens?: number | null;
  cost_credits?: number | null;  // Cost in OpenRouter credits
  cached_tokens?: number | null;  // For cached token tracking
  reasoning_tokens?: number | null;  // For reasoning token tracking
}

export interface TaskStatusResponse {
  id: number;
  prompt: string;
  system_prompt?: string;  // Add system_prompt field
  status: string;
  requested_models?: string[] | null;
  created_at: string;
  updated_at?: string | null;
  generations: Generation[];
  evaluations: Evaluation[];
}

export interface EvaluateRequest {
  evaluator_models: string[];
  evaluation_used_blind_ids: boolean;
  custom_evaluation_prompt?: string;  // New: Optional custom evaluation prompt
}

export interface EvaluateResponse {
  evaluation_id: number;
  status: string;
  message: string;
}

export interface Ranking {
  id: number;
  evaluation_id: number;
  evaluator_model_id: string;
  ranked_list_json: number[]; // Changed from string[] to number[] (generation IDs)
  reasoning_text: string | null;
  error_message: string | null;
  created_at: string;

  // Usage statistics fields
  prompt_tokens?: number | null;
  completion_tokens?: number | null;
  total_tokens?: number | null;
  cost_credits?: number | null;  // Cost in OpenRouter credits
  cached_tokens?: number | null;  // For cached token tracking
  reasoning_tokens?: number | null;  // For reasoning token tracking
}

export interface Evaluation {
  id: number;
  task_id: number;
  status: string; // TaskStatusEnum as string
  evaluation_used_blind_ids: boolean;
  evaluation_prompt?: string;  // New: Store the evaluation prompt used
  created_at: string;
  updated_at: string | null;
  rankings: Ranking[];
}

export interface EvaluationReportResponse {
  evaluation_id: number;
  task_id: number;
  status: string;
  rankings: Ranking[];
}

export interface ModelsListResponse {
  models: string[];
}

export interface TaskHistoryItem {
  id: number;
  prompt_snippet: string;
  status: string; // TaskStatusEnum as string
  created_at: string; // ISO date string
}

export interface TaskHistoryResponse {
  history: TaskHistoryItem[];
}

export interface EvaluationStatusResponse {
  evaluation_id: number;
  task_id: number;
  status: string; // TaskStatusEnum as string
}

// --- Aggregation Schemas (matching backend) ---
export enum AggregationAlgorithmEnum {
  AVERAGE_RANK = "average_rank",
  BORDA_COUNT = "borda_count",
  WEIGHTED_AVERAGE_RANK = "weighted_average_rank",
  COPELAND_METHOD = "copeland_method",
}

export interface AggregatedRankingItem {
  model_id_used: string;
  aggregated_score: number;
  original_ranks: Record<string, number>; // evaluator_model_id to rank
  rank: number;
}

export interface EvaluatorConsistency {
  evaluator_model_id: string;
  consistency_score?: number | null;
  error_message?: string | null;
}

export interface EvaluationAggregationRequest {
  algorithm: AggregationAlgorithmEnum;
  evaluator_weights?: Record<string, number>; // Optional: model_id -> weight
}

export interface EvaluationAggregationResponse {
  evaluation_id: number;
  task_id: number;
  algorithm_used: AggregationAlgorithmEnum;
  aggregated_rankings: AggregatedRankingItem[];
  evaluator_consistencies: EvaluatorConsistency[];
  overall_consistency?: number | null;
  error_message?: string | null;
}

// Usage Statistics Interfaces
export interface TaskUsageStatsResponse {
  task_id: number;
  total_prompt_tokens: number;
  total_completion_tokens: number;
  total_tokens: number;
  total_cost_credits: number;
  total_cached_tokens: number;
  total_reasoning_tokens: number;
  generation_count: number;
  ranking_count: number;
  models_used: string[];
  evaluators_used: string[];
  average_cost_per_generation: number;
}
// --- End Aggregation Schemas ---

// API methods
export const api = {
  // Get available models
  getModels: async (): Promise<string[]> => {
    try {
      const response = await apiClient.get<ModelsListResponse>('/tasks/models');
      return response.data.models;
    } catch (error) {
      console.error('Error fetching models:', error);
      throw error;
    }
  },

  // Create a new task
  createTask: async (prompt: string, selectedModels?: string[], systemPrompt?: string): Promise<TaskCreateResponse> => {
    try {
      const payload: TaskCreateRequest = { 
        prompt,
      };
      
      if (systemPrompt) {
        payload.system_prompt = systemPrompt;
      }
      
      if (selectedModels && selectedModels.length > 0) {
        payload.models_to_generate = selectedModels;
      }
      
      const response = await apiClient.post<TaskCreateResponse>('/tasks', payload);
      return response.data;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  },

  // Evaluate task outputs
  evaluateTask: async (taskId: number, evaluatorModels: string[], evaluationUsedBlindIds: boolean, customEvaluationPrompt?: string): Promise<EvaluateResponse> => {
    try {
      const payload: EvaluateRequest = {
        evaluator_models: evaluatorModels,
        evaluation_used_blind_ids: evaluationUsedBlindIds
      };
      
      if (customEvaluationPrompt) {
        payload.custom_evaluation_prompt = customEvaluationPrompt;
      }
      
      const response = await apiClient.post<EvaluateResponse>(
        `/tasks/${taskId}/evaluate`,
        payload
      );
      return response.data;
    } catch (error) {
      console.error(`Error evaluating task ${taskId}:`, error);
      throw error;
    }
  },

  // Get evaluation report
  getEvaluationReport: async (evaluationId: number): Promise<EvaluationReportResponse> => {
    try {
      const response = await apiClient.get<EvaluationReportResponse>(`/evaluations/${evaluationId}/report`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching report for evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Get evaluation status
  async getEvaluationStatus(evaluationId: number): Promise<EvaluationStatusResponse> {
    try {
      const response = await apiClient.get<EvaluationStatusResponse>(`/evaluations/${evaluationId}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching status for evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Fetch task history
  getTaskHistory: async (limit: number = 50, skip: number = 0): Promise<TaskHistoryResponse> => {
    try {
      const response = await apiClient.get<TaskHistoryResponse>('/tasks/history', {
        params: { limit, skip },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching task history:', error);
      throw error;
    }
  },

  // Get FULL task details (renaming slightly for clarity vs history)
  getTaskDetails: async (taskId: number): Promise<TaskStatusResponse> => {
    try {
      const response = await apiClient.get<TaskStatusResponse>(`/tasks/${taskId}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching details for task ${taskId}:`, error);
      throw error;
    }
  },

  // Delete a task
  deleteTask: async (taskId: number): Promise<void> => {
    await apiClient.delete(`/tasks/${taskId}`);
  },

  // Aggregate evaluation results
  getAggregatedEvaluationReport: async (
    evaluationId: number, 
    algorithm: AggregationAlgorithmEnum,
    evaluatorWeights?: Record<string, number> // Optional weights parameter
  ): Promise<EvaluationAggregationResponse> => {
    try {
      const requestBody: EvaluationAggregationRequest = { algorithm };
      if (evaluatorWeights && Object.keys(evaluatorWeights).length > 0) {
        requestBody.evaluator_weights = evaluatorWeights;
      }
      const response = await apiClient.post<EvaluationAggregationResponse>(
        `/tasks/evaluations/${evaluationId}/aggregate`,
        requestBody
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching aggregated report for evaluation ${evaluationId} with algo ${algorithm}:`, error);
      throw error;
    }
  },

  // Usage statistics endpoint
  getTaskUsageStats: async (taskId: number): Promise<TaskUsageStatsResponse> => {
    try {
      const response = await apiClient.get<TaskUsageStatsResponse>(`/tasks/${taskId}/usage`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching usage stats for task ${taskId}:`, error);
      throw error;
    }
  },

  // System health check
  getHealth: async (): Promise<HealthResponse> => {
    try {
      const response = await apiClient.get<HealthResponse>('/health');
      return response.data;
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  }
}; 

// --- SSE Streaming --- 

// Define the structure of the SSE data events
export interface StreamEventData { 
  model: string;
  chunk?: string; // Present for 'message' events
  reasoning_detail?: string;
  status?: 'DONE' | 'ERROR' | 'CLOSED'; // Present for 'done', 'error', 'closed' events
  content?: string | null; // Error message or other content for status events
}

export type StreamEventHandler = (data: StreamEventData) => void;
export type ErrorEventHandler = (error: Event) => void;
export type OpenEventHandler = () => void;

export const sse = {
  connect: (
    taskId: number,
    onMessage: StreamEventHandler, // Callback for incoming text chunks
    onDone: StreamEventHandler,    // Callback for stream completion for a model
    onError: StreamEventHandler,   // Callback for errors during stream for a model
    onConnectionError: ErrorEventHandler, // Callback for connection-level errors
    onOpen: OpenEventHandler       // Callback when connection opens
  ): EventSource => {
    const url = `/api/v1/tasks/${taskId}/stream`;
    console.log(`Connecting to SSE endpoint: ${url}`);
    
    try {
      // Ensure any existing connections are closed
      try {
        const existingEs = (window as unknown as { [key: string]: EventSource | undefined })[`sse_connection_${taskId}`];
        if (existingEs) {
          console.log(`Closing existing SSE connection for task ${taskId}`);
          existingEs.close();
        }
      } catch (e) {
        console.error(`Error closing existing SSE connection for task ${taskId}:`, e);
      }
      
      const eventSource = new EventSource(url);
      // Save reference to connection for later access
      (window as unknown as { [key: string]: EventSource })[`sse_connection_${taskId}`] = eventSource;

      eventSource.onopen = () => {
        console.log(`SSE connection opened for task ${taskId}`);
        onOpen();
      };

      // Handler for regular message chunks
      eventSource.addEventListener('message', (event) => {
        try {
          const data: StreamEventData = JSON.parse(event.data);
          // console.log(`SSE message event received for task ${taskId}, model: ${data.model}, chunk: "${(data.chunk || '').substring(0, 20)}..."`);
          onMessage(data);
        } catch (e) {
          console.error('Failed to parse SSE message data:', event.data, e);
        }
      });

      // Handler for 'done' status from a specific model stream
      eventSource.addEventListener('done', (event) => {
        try {
          const data: StreamEventData = JSON.parse(event.data);
          console.log(`SSE done event received for task ${taskId}, model: ${data.model}`);
          onDone(data);
        } catch (e) {
          console.error('Failed to parse SSE done data:', event.data, e);
        }
      });

      // Handler for 'error' status from a specific model stream or task
      eventSource.addEventListener('error', (event: Event | { data?: string }) => {
         // Two types of errors:
         // 1. Model-specific errors sent from the backend
         // 2. Connection-level errors (browser's EventSource also triggers 'error')
         if ('data' in event && event.data) {
           try {
             // This is a model-specific error sent from the backend
             const data: StreamEventData = JSON.parse(event.data as string);
             console.error(`SSE error event received for task ${taskId}, model: ${data.model}, content: ${data.content}`);
             onError(data);
           } catch (e) {
             console.error('Failed to parse SSE error data:', event.data, e);
           }
         } else {
           // This is likely a connection-level error
           console.error(`SSE connection error for task ${taskId}:`, event);
           onConnectionError(event as Event);
         }
      });

      return eventSource;
    } catch (e) {
      console.error(`Error establishing SSE connection for task ${taskId}:`, e);
      throw e;
    }
  }
};

// --- End SSE Streaming --- 

export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  UNKNOWN = 'unknown'
}

export interface HealthResponse {
  status: HealthStatus;
  timestamp: string;
  version: string;
  database: {
    connection: {
      status: string;
      message: string;
    };
    tables: {
      status: string;
      message: string;
    };
  };
  services: {
    api: {
      status: string;
      message: string;
    };
  };
}

// Task History Response