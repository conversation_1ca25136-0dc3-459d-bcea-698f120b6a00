import React, { useRef } from 'react';
import LoadingSpinner from './LoadingSpinner';
import SingleOutputDisplay from './SingleOutputDisplay';
import { Generation } from '../api/apiClient';
import { Resizable } from 're-resizable';

interface OutputDisplayProps {
  selectedGeneration: Generation | null;
  isLoading: boolean;
  showScrollbar?: boolean;
}

const OutputDisplay: React.FC<OutputDisplayProps> = ({ 
  selectedGeneration, 
  isLoading, 
  showScrollbar = true 
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  if (isLoading && !selectedGeneration) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner message="Generating responses..." size="large" />
      </div>
    );
  }

  if (!selectedGeneration) {
    return (
      <div className="bg-light-component-subtle dark:bg-dark-component-subtle p-6 rounded-lg border border-light-border dark:border-dark-border text-center">
        <p className="text-light-secondary dark:text-dark-secondary py-8">
          No outputs generated yet. Please wait for model responses.
        </p>
      </div>
    );
  }

  // Show all mode: don't use Resizable, display content directly
  if (!showScrollbar) {
    return (
      <div className="rounded-xl border border-light-border/60 dark:border-dark-border/60 bg-gradient-to-br from-light-component to-light-background dark:from-dark-component dark:to-dark-background shadow-sm">
        <div 
          ref={containerRef} 
          className="p-6 sm:p-8 text-light-primary dark:text-dark-primary"
        >
          {selectedGeneration && (
            <SingleOutputDisplay generation={selectedGeneration} />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-4">
      <Resizable
        defaultSize={{
          width: '100%',
          height: 500,
        }}
        minHeight={300}
        enable={{
          top: false,
          right: false,
          bottom: true,
          left: false,
          topRight: false,
          bottomRight: false,
          bottomLeft: false,
          topLeft: false,
        }}
        className="rounded-xl border border-light-border/60 dark:border-dark-border/60 bg-gradient-to-br from-light-component to-light-background dark:from-dark-component dark:to-dark-background shadow-sm overflow-hidden"
        handleStyles={{
          bottom: {
            bottom: 0,
            height: '8px',
            cursor: 'ns-resize',
          }
        }}
        handleClasses={{
          bottom: 'bg-light-component-subtle/50 dark:bg-dark-component-subtle/50 hover:bg-light-accent/20 dark:hover:bg-dark-accent/20'
        }}
      >
        <div 
          ref={containerRef} 
          className="h-full overflow-auto p-6 sm:p-8 text-light-primary dark:text-dark-primary custom-scrollbar"
        >
          {selectedGeneration && (
            <SingleOutputDisplay generation={selectedGeneration} />
          )}
        </div>
      </Resizable>
      
      <div className="flex items-center justify-center text-xs text-light-secondary dark:text-dark-secondary pt-2">
        <svg className="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M5 5a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zM5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zM6 15a1 1 0 100 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
        </svg>
        Drag edge to resize
      </div>
    </div>
  );
};

export default OutputDisplay; 