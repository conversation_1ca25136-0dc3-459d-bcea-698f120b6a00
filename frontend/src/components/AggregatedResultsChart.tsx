import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer, Cell } from 'recharts';
import { EvaluationAggregationResponse, AggregationAlgorithmEnum } from '../api/apiClient';
import { useTheme } from '../context/ThemeContext';

interface AggregatedResultsChartProps {
  aggregationData: EvaluationAggregationResponse | null;
}

const getModelDisplayName = (modelId: string) => {
  const parts = modelId.split('/');
  return parts.length > 1 ? parts[1] : modelId;
};

// Define a color palette for the bars
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#A4DE6C', '#D0ED57', '#FFC658'];

const AggregatedResultsChart: React.FC<AggregatedResultsChartProps> = ({ aggregationData }) => {
  const { resolvedTheme } = useTheme();
  const tickColor = resolvedTheme === 'dark' ? '#A0AEC0' : '#4A5568'; // Gray-500 for dark, Gray-700 for light

  if (!aggregationData || !aggregationData.aggregated_rankings || aggregationData.aggregated_rankings.length === 0) {
    if (aggregationData?.error_message) {
        return <p className="text-sm text-light-error dark:text-dark-error">Error loading aggregation: {aggregationData.error_message}</p>;
    }
    return <p className="text-sm text-light-secondary dark:text-dark-secondary">No aggregation data to display.</p>;
  }

  const chartData = aggregationData.aggregated_rankings.map(item => ({
    name: getModelDisplayName(item.model_id_used),
    score: parseFloat(item.aggregated_score.toFixed(3)), // Keep some precision
    rank: item.rank,
  }));

  // Determine sort order and score label based on algorithm
  let higherIsBetter = false;
  let scoreLabel = "Average Rank (Lower is Better)";
  if (aggregationData.algorithm_used === AggregationAlgorithmEnum.BORDA_COUNT) {
    higherIsBetter = true;
    scoreLabel = "Borda Score (Higher is Better)";
  }
  
  // Sort data for display - Recharts displays X-axis categories in the order they appear in the data array.
  // For ranks (lower is better), we want the best rank (1) first.
  // For scores where higher is better, we might want to sort descending by score.
  // For now, let's sort by the final rank given by the backend.
  chartData.sort((a, b) => a.rank - b.rank);

  // Type for the legend formatter's arguments
  const legendFormatter = (value: string, entry: any, index: number): React.ReactNode => {
    return <span style={{ color: tickColor }}>{scoreLabel}</span>;
  };

  return (
    <div style={{ width: '100%', height: 300 + chartData.length * 20 }}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart 
          data={chartData} 
          layout="vertical" 
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={resolvedTheme === 'dark' ? '#4A5568' : '#E2E8F0'} />
          <XAxis type="number" stroke={tickColor} domain={higherIsBetter ? [0, 'dataMax + 1'] : undefined} reversed={!higherIsBetter} />
          <YAxis dataKey="name" type="category" stroke={tickColor} width={220} interval={0} />
          <Tooltip 
            contentStyle={{ 
              backgroundColor: resolvedTheme === 'dark' ? 'rgba(45, 55, 72, 0.8)' : 'rgba(255, 255, 255, 0.8)', 
              borderColor: resolvedTheme === 'dark' ? '#4A5568' : '#E2E8F0',
              color: resolvedTheme === 'dark' ? '#E2E8F0' : '#1A202C',
              borderRadius: '0.375rem'
            }}
            formatter={(value: number, name: string) => {
                if (name === 'score') return [value, scoreLabel];
                return [value, name];
            }}
          />
          <Legend formatter={legendFormatter} />
          <Bar dataKey="score" name={scoreLabel} fill="#8884d8">
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default AggregatedResultsChart; 