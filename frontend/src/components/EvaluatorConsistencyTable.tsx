import React from 'react';
import { EvaluationAggregationResponse, EvaluatorConsistency } from '../api/apiClient';
import { useTheme } from '../context/ThemeContext'; // For potential theme-based styling

interface EvaluatorConsistencyTableProps {
  aggregationData: EvaluationAggregationResponse | null;
}

const getModelDisplayName = (modelId: string) => {
  const parts = modelId.split('/');
  return parts.length > 1 ? parts[1] : modelId;
};

const EvaluatorConsistencyTable: React.FC<EvaluatorConsistencyTableProps> = ({ aggregationData }) => {
  const { resolvedTheme } = useTheme();

  if (!aggregationData || !aggregationData.evaluator_consistencies || aggregationData.evaluator_consistencies.length === 0) {
    // No error message check here, as this table might be empty even if aggregation partially succeeded
    return <p className="text-sm text-light-secondary dark:text-dark-secondary">No evaluator consistency data available.</p>;
  }

  const consistencies = aggregationData.evaluator_consistencies;

  return (
    <div className="overflow-x-auto rounded-xl border border-light-border/60 dark:border-dark-border/60 bg-light-component dark:bg-dark-component shadow-sm">
      <table className="min-w-full divide-y divide-light-border dark:divide-dark-border">
        <thead className="bg-light-component-subtle dark:bg-dark-component-subtle">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-light-secondary dark:text-dark-secondary uppercase tracking-wider">
              Evaluator Model
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-light-secondary dark:text-dark-secondary uppercase tracking-wider">
              Consistency Score (Kendall Tau)
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-light-secondary dark:text-dark-secondary uppercase tracking-wider">
              Notes
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-light-border dark:divide-dark-border">
          {consistencies.map((item: EvaluatorConsistency, index: number) => (
            <tr key={item.evaluator_model_id + index} className="hover:bg-light-hover dark:hover:bg-dark-hover">
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-light-primary dark:text-dark-primary">
                {getModelDisplayName(item.evaluator_model_id)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-light-primary dark:text-dark-primary">
                {item.consistency_score !== null && item.consistency_score !== undefined 
                  ? item.consistency_score.toFixed(3) 
                  : 'N/A'}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-light-secondary dark:text-dark-error">
                {item.error_message ? (
                    <span title={item.error_message} className="cursor-help">
                        {item.error_message.substring(0,50)}{item.error_message.length > 50 ? '...' : ''}
                    </span>
                ) : (
                    item.consistency_score === null || item.consistency_score === undefined ? 'Could not be computed' : '-'
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {aggregationData.overall_consistency !== null && aggregationData.overall_consistency !== undefined && (
        <div className="px-6 py-3 bg-light-component-subtle dark:bg-dark-component-subtle border-t border-light-border dark:border-dark-border text-sm text-light-primary dark:text-dark-primary font-medium">
            Overall Average Consistency: {aggregationData.overall_consistency.toFixed(3)}
        </div>
      )}
    </div>
  );
};

export default EvaluatorConsistencyTable; 