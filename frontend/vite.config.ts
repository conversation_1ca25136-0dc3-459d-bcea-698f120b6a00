import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { fileURLToPath } from 'url'
import { dirname, resolve } from 'path'

// Get current directory name in ESM
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      // Setup alias consistent with tsconfig.json
      '@': resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000, // Specify port for dev server
    proxy: {
      // Proxy API requests to the backend during development
      // Adjust '/api' if your backend prefix is different
      '/api': {
        target: 'http://localhost:8000', // Your FastAPI backend URL
        changeOrigin: true,
        // Optionally rewrite path if needed, e.g., remove /api prefix
        // rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
