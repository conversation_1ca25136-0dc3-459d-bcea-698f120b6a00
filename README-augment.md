# AI Tool Testing Branch: AUGMENT

This branch is configured for testing **augment** AI tool capabilities.

## 🎯 Testing Configuration

- **Database**: `llm_eval_augment` on `postgresql-augment-service:5432`
- **Backend Config**: `k8s/backend-augment.yaml`
- **CI/CD Workflow**: `.github/workflows/ci-cd-augment.yaml`
- **Branch Trigger**: `ai-test/augment`

## 🚀 Quick Start

1. **Verify database is running**:
   ```bash
   kubectl get pods -n llm-eval | grep postgresql-augment
   ```

2. **Reset database to baseline**:
   ```bash
   ./scripts/setup-multi-databases.sh reset augment
   ```

3. **Deploy application**:
   ```bash
   kubectl apply -f k8s/backend-augment.yaml
   ```

4. **Test connection**:
   ```bash
   kubectl exec -n llm-eval deployment/backend-augment -- curl -s http://localhost:8000/api/v1/health
   ```

## 📋 Testing Task: Usage Statistics Implementation

Execute the Usage Statistics functionality implementation challenge:

**Objective**: Add complete usage tracking for OpenRouter API calls in both generations and evaluations.

**Success Criteria**:
- [ ] Backend models updated with usage fields
- [ ] OpenRouter API integration with usage tracking
- [ ] Frontend display of usage statistics
- [ ] Single database migration script
- [ ] All tests passing

**Documentation**: See `docs/ai-tool-test-task-usage-statistics.md`

## 📊 Results Recording

Document your results in `results/augment-usage-stats-results.md`:
- Time taken
- Number of AI interactions needed
- Code quality assessment
- Issues encountered and solutions
- Final working status

## 🔄 Reset Environment

To reset for a fresh test:
```bash
# Reset database
./scripts/setup-multi-databases.sh reset augment

# Redeploy application
kubectl delete -f k8s/backend-augment.yaml
kubectl apply -f k8s/backend-augment.yaml
```
