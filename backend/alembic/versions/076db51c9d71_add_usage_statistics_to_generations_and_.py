"""add_usage_statistics_to_generations_and_rankings

Revision ID: 076db51c9d71
Revises: 28b5937d928a
Create Date: 2025-05-30 22:15:52.265046

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '076db51c9d71'
down_revision: Union[str, None] = '28b5937d928a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('generations', sa.Column('prompt_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('completion_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('total_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('cost_credits', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('cached_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('reasoning_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('prompt_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('completion_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('total_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('cost_credits', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('cached_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('reasoning_tokens', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rankings', 'reasoning_tokens')
    op.drop_column('rankings', 'cached_tokens')
    op.drop_column('rankings', 'cost_credits')
    op.drop_column('rankings', 'total_tokens')
    op.drop_column('rankings', 'completion_tokens')
    op.drop_column('rankings', 'prompt_tokens')
    op.drop_column('generations', 'reasoning_tokens')
    op.drop_column('generations', 'cached_tokens')
    op.drop_column('generations', 'cost_credits')
    op.drop_column('generations', 'total_tokens')
    op.drop_column('generations', 'completion_tokens')
    op.drop_column('generations', 'prompt_tokens')
    # ### end Alembic commands ###
