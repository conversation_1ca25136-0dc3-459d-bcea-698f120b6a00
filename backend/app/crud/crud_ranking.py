from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any

from app.db import models
from app.schemas import task as task_schema # Keeping schema import consistent

async def create_ranking(db: AsyncSession, ranking_in: task_schema.RankingCreate) -> models.Ranking:
    """
    Creates a single ranking record.
    """
    # Note: Pydantic v2 uses model_dump() instead of dict()
    db_ranking = models.Ranking(**ranking_in.model_dump())
    db.add(db_ranking)
    await db.commit()
    await db.refresh(db_ranking)
    return db_ranking

async def bulk_create_rankings(db: AsyncSession, evaluation_id: int, rankings_data: List[Dict[str, Any]]) -> List[models.Ranking]:
    """
    Efficiently creates multiple ranking records for a specific evaluation.
    Expects a list of dictionaries, each with ranking details.
    """
    db_rankings = [
        models.Ranking(
            evaluation_id=evaluation_id,
            evaluator_model_id=rank_data["evaluator_model_id"],
            ranked_list_json=rank_data.get("ranked_list_json"),
            reasoning_text=rank_data.get("reasoning_text"),
            error_message=rank_data.get("error_message")
        ) for rank_data in rankings_data
    ]
    db.add_all(db_rankings)
    await db.commit()
    # Similar to bulk_create_generations, refresh is not automatic.
    return db_rankings # Return list of objects added 