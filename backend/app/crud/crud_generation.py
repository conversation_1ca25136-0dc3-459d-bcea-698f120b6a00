from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List, Dict, Any, Optional

from app.db import models
from app.schemas import task as task_schema # Keeping schema import consistent

async def create_generation(db: AsyncSession, generation_in: task_schema.GenerationCreate) -> models.Generation:
    """
    Creates a single generation record.
    """
    db_generation = models.Generation(**generation_in.model_dump())
    db.add(db_generation)
    await db.commit()
    await db.refresh(db_generation)
    return db_generation

async def bulk_create_generations(db: AsyncSession, task_id: int, generations_data: List[Dict[str, Any]]) -> List[models.Generation]:
    """
    Efficiently creates multiple generation records for a specific task.
    Expects a list of dictionaries, each containing model_id_used, output_text, error_message, and optionally blind_id.
    """
    db_generations = [
        models.Generation(
            task_id=task_id,
            model_id_used=gen_data["model_id_used"],
            output_text=gen_data.get("output_text"),
            reasoning_text=gen_data.get("reasoning_text"),
            error_message=gen_data.get("error_message"),
            blind_id=gen_data["blind_id"],
            # Usage statistics fields
            prompt_tokens=gen_data.get("prompt_tokens"),
            completion_tokens=gen_data.get("completion_tokens"),
            total_tokens=gen_data.get("total_tokens"),
            cost_credits=gen_data.get("cost_credits"),
            cached_tokens=gen_data.get("cached_tokens"),
            reasoning_tokens=gen_data.get("reasoning_tokens")
        ) for gen_data in generations_data
    ]
    db.add_all(db_generations)
    await db.commit()
    # Note: bulk operations don't automatically refresh objects with IDs like single add.
    # We fetch them again if needed, or assume they are created.
    # For simplicity here, we won't refresh them after bulk insert.
    # If IDs are needed immediately, consider adding them individually or fetching after commit.
    return db_generations # Return the list of objects added (without refreshed state)

async def get_generations_for_task(db: AsyncSession, task_id: int) -> List[models.Generation]:
    """
    Retrieves all generations associated with a specific task ID.
    """
    result = await db.execute(
        select(models.Generation).filter(models.Generation.task_id == task_id)
    )
    return result.scalars().all() 