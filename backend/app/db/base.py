from typing import Async<PERSON>enerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from app.core.config import settings

# Create the async engine
# connect_args={"check_same_thread": False} is specific to SQLite for use with FastAPI/multiple threads
engine = create_async_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

# Create a configured "Session" class
AsyncSessionFactory = async_sessionmaker(
    bind=engine,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False, # Good default for async usage
    class_=AsyncSession
)

# Base class for ORM models
class Base(DeclarativeBase):
    pass

# Dependency to get DB session in API endpoints
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency that provides an AsyncSession for use in API endpoints.
    Ensures the session is closed afterwards.
    """
    async with AsyncSessionFactory() as session:
        try:
            yield session
            # Optional: commit here if you want automatic commit per request,
            # but usually better to commit explicitly in CRUD functions/services.
            # await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close() 