from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, ForeignKey, Enum as S<PERSON><PERSON><PERSON>, Boolean, Float
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.db.base import Base

class TaskStatus(str, enum.Enum):
    PENDING = "PENDING"
    GENERATING = "GENERATING"
    COMPLETED = "COMPLETED"
    EVALUATING = "EVALUATING"
    EVALUATION_DONE = "EVALUATION_DONE"
    FAILED = "FAILED"

class Task(Base):
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, index=True)
    prompt = Column(Text, nullable=False)
    system_prompt = Column(Text, nullable=True)  # New field for system prompt
    status = Column(SQLEnum(TaskStatus), default=TaskStatus.PENDING, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    generations = relationship("Generation", back_populates="task", cascade="all, delete-orphan")
    evaluations = relationship("Evaluation", back_populates="task", cascade="all, delete-orphan")


class Generation(Base):
    __tablename__ = "generations"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False)
    model_id_used = Column(String, nullable=False, index=True) # e.g., "openai/gpt-4o"
    blind_id = Column(String, nullable=False, index=True, unique=True) # Added for Blind-ID feature
    output_text = Column(Text)
    reasoning_text = Column(Text, nullable=True) # Added to store reasoning for the generation itself
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Usage statistics fields
    prompt_tokens = Column(Integer, nullable=True)
    completion_tokens = Column(Integer, nullable=True)
    total_tokens = Column(Integer, nullable=True)
    cost_credits = Column(Float, nullable=True)  # Cost in OpenRouter credits (changed from Integer to Float)
    cached_tokens = Column(Integer, nullable=True)  # For cached token tracking
    reasoning_tokens = Column(Integer, nullable=True)  # For reasoning token tracking

    task = relationship("Task", back_populates="generations")


class Evaluation(Base):
    __tablename__ = "evaluations"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False)
    status = Column(SQLEnum(TaskStatus), default=TaskStatus.PENDING, nullable=False) # Reuse TaskStatus or create dedicated EvaluationStatus
    evaluation_used_blind_ids = Column(Boolean, nullable=False, server_default='false')
    evaluation_prompt = Column(Text, nullable=True)  # Store the evaluation prompt used
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    task = relationship("Task", back_populates="evaluations")
    rankings = relationship("Ranking", back_populates="evaluation", cascade="all, delete-orphan")


class Ranking(Base):
    __tablename__ = "rankings"

    id = Column(Integer, primary_key=True, index=True)
    evaluation_id = Column(Integer, ForeignKey("evaluations.id"), nullable=False)
    evaluator_model_id = Column(String, nullable=False, index=True) # Which model did the ranking
    ranked_list_json = Column(JSONB, nullable=True) # Changed from JSON to JSONB
    reasoning_text = Column(Text) # Store the explanation from the evaluator model
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Usage statistics fields
    prompt_tokens = Column(Integer, nullable=True)
    completion_tokens = Column(Integer, nullable=True)
    total_tokens = Column(Integer, nullable=True)
    cost_credits = Column(Float, nullable=True)  # Cost in OpenRouter credits (changed from Integer to Float)
    cached_tokens = Column(Integer, nullable=True)  # For cached token tracking
    reasoning_tokens = Column(Integer, nullable=True)  # For reasoning token tracking

    evaluation = relationship("Evaluation", back_populates="rankings")